---
creation_date: 2025-09-03
modification_date: 2025-09-03
type: documentation
aliases: [AI Agents, Agent Guidelines, AI Assistant Guidelines]
tags: [cruca, church, documentation, ai, agents, automation, guidelines]
status: active
version: 1.0
author: <PERSON>
reviewers: []
links: []
related: [Report Generator Assistant Prompt (Augment Ai), Social Media Automation tools, Templater Auto-Processing Setup Guide]
---

# AI Agents Guidelines & Documentation

## Overview
This document provides comprehensive guidelines for AI agents and assistants working within the CRUCA (Caboolture Region Uniting Church Australia) documentation system. It ensures consistency, proper formatting, and effective integration with the Obsidian vault structure and church administrative workflows.

## Purpose
- Maintain consistency across all AI-generated content
- Ensure proper integration with Obsidian vault structure and extensions
- Provide clear guidelines for AI agents working on church-related tasks
- Establish standards for documentation, reporting, and administrative assistance

## Scope
This documentation covers AI agent behavior for:
- Administrative task reporting and documentation
- Church procedure documentation
- Project management and tracking
- Communication and correspondence assistance
- Automation and workflow optimization

## Core Agent Behavior Guidelines

### 1. Documentation Standards
- **Format**: All content must be compatible with Obsidian markdown
- **Structure**: Follow PARA method organization (Projects, Areas, Resources, Archive)
- **Links**: Use `[[WikiLink]]` syntax for internal references
- **Metadata**: Include comprehensive YAML frontmatter for all documents

### 2. Memory Management
- Update critical context after each response
- Flag outdated information with `[PENDING-DELETION:reason]` before removal
- Retain flagged memories for 3-5 interactions
- Structure memory entries as: `[CATEGORY] Information`

#### Memory Categories
- `[PREFERENCE]` - User preferences and style
- `[PROJECT]` - Project requirements and architecture
- `[WORKFLOW]` - Development patterns
- `[CONTEXT]` - Current work state
- `[CHURCH]` - Church-specific context and procedures

### 3. Update Triggers
- Project scope changes
- Implementation updates
- Tool/framework switches
- Security concerns
- User preference changes
- Church policy updates

## Vault-Specific Configuration

### Obsidian Vault Layout
- **Organization**: PARA method implementation with minimal subfolders
- **Navigation**: Extensive use of Obsidian Links (`[[WikiLink]]` syntax)
- **Visualization**: Dataview queries for linking and visualizing related information
- **Templates**: Enhanced templates with Templater plugin integration
- **Cross-referencing**: Wiki-style linking between related documents

### Folder Structure Guidelines
```
1-Projects/     - Time-limited goals with defined outcomes
2-Areas/        - Ongoing responsibilities (Administration, Hall Hire)
3-Resources/    - Reference materials and procedures
4-Archive/      - Completed or inactive items
Templates/      - Obsidian templates for different note types
0-Journals-Daily Notes/ - Daily work logs and notes
```

### Required Metadata Format
```yaml
---
creation_date: YYYY-MM-DD
modification_date: YYYY-MM-DD
type: [documentation|resource|project|area|template]
aliases: [Alternative names]
tags: [cruca, church, relevant-tags]
status: [draft|active|completed|archived]
version: X.X
author: Jordan Pacey
related: [Related documents]
---
```

## Church Context & User Profile

### Organization Details
- **Entity**: Caboolture Region Uniting Church Australia (CRUCA)
- **Locations**: Beachmere, Caboolture, Upper Caboolture Uniting Church
- **Administrator**: Jordan Pacey (Office Administrator)
- **Focus Areas**: Innovation, systems, software development, problem solving

### User Characteristics
- New to professional office work
- Lateral thinker with focus on innovation
- Preferred communication style: courteous, reserved, modest, assistive
- Technical interests: automation, systems improvement, documentation

### Project Goals
- Improve outdated church task and records systems
- Enhance communication with church community
- Assist community in accessing church services
- Increase church visibility and community engagement
- Implement online services and transparent information sharing

## Task Management Integration

### Todoist Integration
- All tasks and correspondence requests noted in Todoist
- Cross-platform synchronization (email clients, mobile, web)
- Regular exports for reporting and documentation
- Archive completed tasks for historical reference

### Obsidian Documentation
- Serves as "Second Brain" for church administration
- Export capabilities to Microsoft Word, PDF via Pandoc
- Comprehensive linking and cross-referencing
- Dataview queries for dynamic content organization

## Communication Style Guidelines

### Report Writing
- Provide concise paragraphs with clear messaging
- Show evidence of abilities and competencies
- Include specific examples of methods and actions taken
- Focus on best practices and effective solutions
- Demonstrate task management methods and workflows

### Documentation Standards
- Use professional, clear language appropriate for church council
- Provide transparency in administrative efforts
- Include specific achievements and progress metrics
- Outline future plans and recommendations
- Maintain modest, assistive tone while showing competency

## Automation & Integration Guidelines

### Supported Automations
- **Templater**: Auto-processing of template placeholders
- **Dataview**: Dynamic content queries and visualization
- **Social Media**: Buffer, Publer integration planning
- **Bulletin**: Automated content population from databases
- **Calendar**: Google Calendar/Microsoft Outlook integration

### Technical Considerations
- Maintain compatibility with existing Obsidian plugins
- Ensure cross-platform functionality (Windows, mobile)
- Support export workflows (Pandoc, Word, PDF)
- Integrate with existing church systems and workflows

## Quality Assurance

### Content Review Checklist
- [ ] Proper YAML frontmatter included
- [ ] Appropriate tags and categories applied
- [ ] WikiLinks properly formatted and functional
- [ ] Dataview queries tested and working
- [ ] Content appropriate for church context
- [ ] Professional tone and language maintained
- [ ] Related documents properly linked

### Version Control
- Track document versions in metadata
- Note significant changes in modification_date
- Maintain revision history for important documents
- Archive outdated versions appropriately

## Related Resources
- [[3-Resources/Report Generator Assistant Prompt (Augment Ai)|Report Generator Guidelines]]
- [[2-Areas/Social Media Automation tools|Social Media Automation]]
- [[3-Resources/Templater Auto-Processing Setup Guide|Templater Setup]]
- [[Templates/Templates TOC|Template Library]]
- [[3-Resources/Task Format Guide - Dataview vs Tasks Plugin|Task Management]]

## Quick Reference Links
- [[README|Vault Home]]
- [[1-Projects/Projects TOC|Projects]]
- [[2-Areas/Areas TOC|Areas]]
- [[3-Resources/Resources TOC|Resources]]
- [[Templates/Templates TOC|Templates]]

---

## Revision History
| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0 | 2025-09-03 | Jordan Pacey | Initial comprehensive agent guidelines document |

## Notes
This document should be reviewed and updated regularly to reflect changes in church procedures, technology implementations, and administrative workflows. All AI agents should reference this document before beginning work within the CRUCA vault system.
